["tests/test_data_fetching_fixes.py::TestDataFetchingFixes::test_auto_all_symbols_with_date_fixes", "tests/test_data_fetching_fixes.py::TestDataFetchingFixes::test_date_parsing_fallback_to_days", "tests/test_data_fetching_fixes.py::TestDataFetchingFixes::test_date_parsing_future_dates", "tests/test_data_fetching_fixes.py::TestDataFetchingFixes::test_date_parsing_same_day", "tests/test_data_fetching_fixes.py::TestDataFetchingFixes::test_date_validation_warnings_only", "tests/test_data_fetching_fixes.py::TestDataFetchingFixes::test_fetch_data_with_symbol_classification", "tests/test_data_fetching_fixes.py::TestDataFetchingFixes::test_market_type_validation", "tests/test_data_fetching_fixes.py::TestDataFetchingFixes::test_process_bulk_results_partial_failure", "tests/test_data_fetching_fixes.py::TestDataFetchingFixes::test_process_bulk_results_success", "tests/test_data_fetching_fixes.py::TestDataFetchingFixes::test_symbol_format_help_display", "tests/test_enhanced_pattern_matching.py::test_edge_cases", "tests/test_enhanced_pattern_matching.py::test_special_character_patterns", "tests/test_main_args.py::test_argument_combinations", "tests/test_main_args.py::test_simple_symbol_processing", "tests/test_new_functionality.py::test_cli_operations", "tests/test_new_functionality.py::test_data_management_service", "tests/test_new_functionality.py::test_fyers_symbol_construction", "tests/test_new_functionality.py::test_symbol_validation", "tests/test_refactored_system.py::TestRefactoredSystem::test_command_dispatcher_initialization", "tests/test_refactored_system.py::TestRefactoredSystem::test_command_dispatcher_routing", "tests/test_refactored_system.py::TestRefactoredSystem::test_data_fetch_handler_with_valid_symbols", "tests/test_refactored_system.py::TestRefactoredSystem::test_data_fetch_operation_detection", "tests/test_refactored_system.py::TestRefactoredSystem::test_date_parsing_edge_cases", "tests/test_refactored_system.py::TestRefactoredSystem::test_error_handling_in_handlers", "tests/test_refactored_system.py::TestRefactoredSystem::test_logging_and_timing", "tests/test_refactored_system.py::TestRefactoredSystem::test_maintenance_handler_nse_processing", "tests/test_refactored_system.py::TestRefactoredSystem::test_maintenance_operation_detection", "tests/test_refactored_system.py::TestRefactoredSystem::test_market_type_validation", "tests/test_refactored_system.py::TestRefactoredSystem::test_performance_optimizations_applied", "tests/test_refactored_system.py::TestRefactoredSystem::test_system_handler_view_data", "tests/test_refactored_system.py::TestRefactoredSystem::test_system_operation_detection", "tests/test_space_symbol_support.py::test_edge_cases", "tests/test_space_symbol_support.py::test_original_symbols_still_work", "tests/test_space_symbol_support.py::test_space_containing_symbols", "tests/test_symbol_classifier_simple.py::test_classifier", "tests/test_symbol_data_fetching.py::test_data_fetching", "tests/test_symbol_data_fetching.py::test_symbol_classification", "tests/test_symbol_mapping_validation.py::test_fix_fyers_symbols_command", "tests/test_symbol_mapping_validation.py::test_symbol_mapping_consistency", "tests/test_usage_examples.py::test_auto_processing_commands", "tests/test_usage_examples.py::test_data_management_commands", "tests/test_usage_examples.py::test_fetch_commands", "tests/test_usage_examples.py::test_fix_commands", "tests/test_usage_examples.py::test_other_commands"]