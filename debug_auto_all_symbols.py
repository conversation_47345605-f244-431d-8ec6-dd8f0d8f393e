#!/usr/bin/env python3
"""
Debug script to identify issues with --auto-all-symbols command.
This script will systematically check each component to find the root cause.
"""

import sys
import os
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.logging import get_logger, setup_enhanced_logging
from src.database.connection import get_db
from src.database.models import SymbolMapping, MarketType
from src.services.fyers_auth_service import FyersAuthService
from src.services.bulk_data_service import BulkDataService
from src.helpers.cli_operations import CLIOperations
from sqlalchemy import and_

# Setup logging
setup_enhanced_logging()
logger = get_logger(__name__)


class AutoAllSymbolsDebugger:
    """Comprehensive debugger for auto-all-symbols functionality."""
    
    def __init__(self):
        self.db = next(get_db())
        self.cli_ops = CLIOperations()
        self.fyers_auth = FyersAuthService()
        self.bulk_service = BulkDataService()
        
    def __del__(self):
        if hasattr(self, 'db'):
            self.db.close()
    
    def run_comprehensive_diagnosis(self, market_type: str = "INDEX", limit: int = 5) -> Dict[str, Any]:
        """Run comprehensive diagnosis of auto-all-symbols functionality."""
        logger.info("🔍 Starting comprehensive diagnosis of auto-all-symbols functionality")
        logger.info("=" * 80)
        
        results = {
            'database_check': {},
            'symbol_mapping_check': {},
            'fyers_auth_check': {},
            'api_connectivity_check': {},
            'data_fetch_test': {},
            'overall_status': 'UNKNOWN'
        }
        
        try:
            # 1. Database connectivity check
            logger.info("\n1️⃣ Checking database connectivity...")
            results['database_check'] = self._check_database_connectivity()
            
            # 2. Symbol mapping table check
            logger.info("\n2️⃣ Checking symbol mapping table...")
            results['symbol_mapping_check'] = self._check_symbol_mapping_table(market_type, limit)
            
            # 3. Fyers authentication check
            logger.info("\n3️⃣ Checking Fyers authentication...")
            results['fyers_auth_check'] = self._check_fyers_authentication()
            
            # 4. API connectivity check
            logger.info("\n4️⃣ Checking API connectivity...")
            results['api_connectivity_check'] = self._check_api_connectivity(market_type)
            
            # 5. Data fetch test
            logger.info("\n5️⃣ Testing data fetch functionality...")
            results['data_fetch_test'] = self._test_data_fetch(market_type, limit)
            
            # 6. Overall assessment
            results['overall_status'] = self._assess_overall_status(results)
            
            # 7. Generate recommendations
            self._generate_recommendations(results)
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Error during diagnosis: {e}")
            results['overall_status'] = 'ERROR'
            results['error'] = str(e)
            return results
    
    def _check_database_connectivity(self) -> Dict[str, Any]:
        """Check database connectivity and basic table existence."""
        try:
            # Test basic query
            result = self.db.execute("SELECT 1").fetchone()
            
            # Check if symbol_mapping table exists
            table_check = self.db.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'symbol_mapping'
                )
            """).fetchone()[0]
            
            # Check if index_ohlcv table exists
            index_table_check = self.db.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'index_ohlcv'
                )
            """).fetchone()[0]
            
            return {
                'status': 'SUCCESS',
                'connection': True,
                'symbol_mapping_exists': table_check,
                'index_ohlcv_exists': index_table_check
            }
            
        except Exception as e:
            logger.error(f"❌ Database connectivity check failed: {e}")
            return {
                'status': 'FAILED',
                'error': str(e),
                'connection': False
            }
    
    def _check_symbol_mapping_table(self, market_type: str, limit: int) -> Dict[str, Any]:
        """Check symbol mapping table for the specified market type."""
        try:
            market_type_enum = MarketType(market_type.upper())
            
            # Count total symbols for market type
            total_count = self.db.query(SymbolMapping).filter(
                and_(
                    SymbolMapping.market_type == market_type_enum,
                    SymbolMapping.is_active == True
                )
            ).count()
            
            # Count symbols with fyers_symbol
            fyers_symbol_count = self.db.query(SymbolMapping).filter(
                and_(
                    SymbolMapping.market_type == market_type_enum,
                    SymbolMapping.is_active == True,
                    SymbolMapping.fyers_symbol.isnot(None)
                )
            ).count()
            
            # Get sample symbols
            sample_symbols = self.db.query(SymbolMapping).filter(
                and_(
                    SymbolMapping.market_type == market_type_enum,
                    SymbolMapping.is_active == True,
                    SymbolMapping.fyers_symbol.isnot(None)
                )
            ).limit(limit).all()
            
            sample_data = []
            for mapping in sample_symbols:
                sample_data.append({
                    'nse_symbol': mapping.nse_symbol,
                    'fyers_symbol': mapping.fyers_symbol,
                    'market_type': mapping.market_type.value
                })
            
            logger.info(f"📊 Symbol mapping summary for {market_type}:")
            logger.info(f"   Total symbols: {total_count}")
            logger.info(f"   With fyers_symbol: {fyers_symbol_count}")
            logger.info(f"   Sample symbols: {len(sample_data)}")
            
            for i, symbol in enumerate(sample_data[:3], 1):
                logger.info(f"   {i}. {symbol['nse_symbol']} -> {symbol['fyers_symbol']}")
            
            return {
                'status': 'SUCCESS',
                'total_count': total_count,
                'fyers_symbol_count': fyers_symbol_count,
                'sample_symbols': sample_data,
                'has_symbols': fyers_symbol_count > 0
            }
            
        except Exception as e:
            logger.error(f"❌ Symbol mapping check failed: {e}")
            return {
                'status': 'FAILED',
                'error': str(e),
                'has_symbols': False
            }
    
    def _check_fyers_authentication(self) -> Dict[str, Any]:
        """Check Fyers authentication status."""
        try:
            # Initialize Fyers auth
            auth_success = self.fyers_auth.initialize()
            
            if not auth_success:
                return {
                    'status': 'FAILED',
                    'authenticated': False,
                    'error': 'Failed to initialize Fyers authentication'
                }
            
            # Check if authenticated
            is_authenticated = self.fyers_auth.is_authenticated()
            
            logger.info(f"🔐 Fyers authentication status: {'✅ Authenticated' if is_authenticated else '❌ Not authenticated'}")
            
            return {
                'status': 'SUCCESS' if is_authenticated else 'FAILED',
                'authenticated': is_authenticated,
                'initialization_success': auth_success
            }
            
        except Exception as e:
            logger.error(f"❌ Fyers authentication check failed: {e}")
            return {
                'status': 'FAILED',
                'error': str(e),
                'authenticated': False
            }

    def _check_api_connectivity(self, market_type: str) -> Dict[str, Any]:
        """Check API connectivity with a sample symbol."""
        try:
            if not self.fyers_auth.is_authenticated():
                return {
                    'status': 'FAILED',
                    'error': 'Fyers not authenticated',
                    'api_reachable': False
                }

            # Get a sample symbol for testing
            market_type_enum = MarketType(market_type.upper())
            sample_mapping = self.db.query(SymbolMapping).filter(
                and_(
                    SymbolMapping.market_type == market_type_enum,
                    SymbolMapping.is_active == True,
                    SymbolMapping.fyers_symbol.isnot(None)
                )
            ).first()

            if not sample_mapping:
                return {
                    'status': 'FAILED',
                    'error': f'No sample symbols found for {market_type}',
                    'api_reachable': False
                }

            # Test API call with sample symbol
            test_symbol = sample_mapping.fyers_symbol
            logger.info(f"🧪 Testing API connectivity with symbol: {test_symbol}")

            # Try to fetch 1 day of data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=1)

            test_data = self.fyers_auth.fetch_historical_data_chunked(
                symbol=test_symbol,
                start_date=start_date,
                end_date=end_date,
                interval=1
            )

            data_received = len(test_data) if test_data else 0
            logger.info(f"📊 API test result: {data_received} data points received")

            return {
                'status': 'SUCCESS' if data_received > 0 else 'WARNING',
                'api_reachable': True,
                'test_symbol': test_symbol,
                'data_points_received': data_received,
                'warning': 'No data received' if data_received == 0 else None
            }

        except Exception as e:
            logger.error(f"❌ API connectivity check failed: {e}")
            return {
                'status': 'FAILED',
                'error': str(e),
                'api_reachable': False
            }

    def _test_data_fetch(self, market_type: str, limit: int) -> Dict[str, Any]:
        """Test the actual data fetch functionality."""
        try:
            logger.info(f"🧪 Testing data fetch for {market_type} with limit {limit}")

            # Use the same date range as the failing command
            start_date = datetime(2025, 7, 25)
            end_date = datetime(2025, 7, 25)

            # Test the CLI operations method directly
            results = self.cli_ops.process_symbols_with_resume_and_dates(
                market_type=market_type,
                start_date=start_date,
                end_date=end_date,
                batch_size=5,
                start_from=0,
                limit=limit
            )

            logger.info(f"📊 Data fetch test results:")
            logger.info(f"   Success: {results.get('success', False)}")
            logger.info(f"   Total symbols: {results.get('total_symbols', 0)}")
            logger.info(f"   Processed: {results.get('processed_symbols', 0)}")
            logger.info(f"   Successful: {results.get('successful_symbols', 0)}")
            logger.info(f"   Failed: {results.get('failed_symbols', 0)}")

            return {
                'status': 'SUCCESS' if results.get('success', False) else 'FAILED',
                'results': results,
                'test_date_range': f"{start_date.date()} to {end_date.date()}"
            }

        except Exception as e:
            logger.error(f"❌ Data fetch test failed: {e}")
            return {
                'status': 'FAILED',
                'error': str(e)
            }

    def _assess_overall_status(self, results: Dict[str, Any]) -> str:
        """Assess overall status based on all checks."""
        checks = [
            results['database_check'].get('status'),
            results['symbol_mapping_check'].get('status'),
            results['fyers_auth_check'].get('status'),
            results['api_connectivity_check'].get('status'),
            results['data_fetch_test'].get('status')
        ]

        if all(status == 'SUCCESS' for status in checks):
            return 'SUCCESS'
        elif any(status == 'FAILED' for status in checks):
            return 'FAILED'
        else:
            return 'WARNING'

    def _generate_recommendations(self, results: Dict[str, Any]) -> None:
        """Generate recommendations based on diagnosis results."""
        logger.info("\n💡 RECOMMENDATIONS:")
        logger.info("=" * 50)

        # Database issues
        if results['database_check'].get('status') == 'FAILED':
            logger.info("❌ Database connectivity issues detected:")
            logger.info("   - Check database connection settings")
            logger.info("   - Verify database is running")
            logger.info("   - Check network connectivity")

        # Symbol mapping issues
        symbol_check = results['symbol_mapping_check']
        if not symbol_check.get('has_symbols', False):
            logger.info("❌ No symbols found in symbol_mapping table:")
            logger.info("   - Run: python main.py --process-nse-symbols")
            logger.info("   - Check if symbol_mapping table is populated")
            logger.info("   - Verify market type filtering")
        elif symbol_check.get('fyers_symbol_count', 0) == 0:
            logger.info("❌ No fyers_symbol values found:")
            logger.info("   - Run: python main.py --fix-fyers-symbols")
            logger.info("   - Check symbol mapping logic")

        # Authentication issues
        if results['fyers_auth_check'].get('status') == 'FAILED':
            logger.info("❌ Fyers authentication issues detected:")
            logger.info("   - Check Fyers API credentials in .env file")
            logger.info("   - Verify Fyers API access token is valid")
            logger.info("   - Check network connectivity to Fyers API")

        # API connectivity issues
        if results['api_connectivity_check'].get('status') == 'FAILED':
            logger.info("❌ API connectivity issues detected:")
            logger.info("   - Check Fyers API rate limits")
            logger.info("   - Verify symbol format compatibility")
            logger.info("   - Check API endpoint availability")

        # Data fetch issues
        if results['data_fetch_test'].get('status') == 'FAILED':
            logger.info("❌ Data fetch functionality issues detected:")
            logger.info("   - Check bulk data service implementation")
            logger.info("   - Verify date range handling")
            logger.info("   - Check error handling in CLI operations")


def main():
    """Main function to run the debugger."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Debug auto-all-symbols functionality")
    parser.add_argument("--market-type", default="INDEX", choices=['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS'],
                       help="Market type to test (default: INDEX)")
    parser.add_argument("--limit", type=int, default=5,
                       help="Limit number of symbols to test (default: 5)")
    
    args = parser.parse_args()
    
    debugger = AutoAllSymbolsDebugger()
    results = debugger.run_comprehensive_diagnosis(args.market_type, args.limit)
    
    logger.info("\n" + "=" * 80)
    logger.info(f"🏁 Diagnosis completed with status: {results['overall_status']}")
    logger.info("=" * 80)


if __name__ == "__main__":
    main()
