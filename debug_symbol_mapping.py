#!/usr/bin/env python3
"""
Debug script to check symbol mapping and test with proper INDEX symbols.
"""

import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.logging import get_logger, setup_enhanced_logging
from src.database.connection import get_db
from src.database.models import SymbolMapping, MarketType
from src.services.fyers_auth_service import FyersAuthService
from sqlalchemy import and_

# Setup logging
setup_enhanced_logging()
logger = get_logger(__name__)


def check_index_symbols():
    """Check what INDEX symbols are available in symbol_mapping."""
    logger.info("🔍 Checking INDEX symbols in symbol_mapping table")
    logger.info("=" * 60)
    
    db = next(get_db())
    try:
        # Get all INDEX symbols
        index_symbols = db.query(SymbolMapping).filter(
            and_(
                SymbolMapping.market_type == MarketType.INDEX,
                SymbolMapping.is_active == True,
                SymbolMapping.fyers_symbol.isnot(None)
            )
        ).all()
        
        logger.info(f"📊 Found {len(index_symbols)} INDEX symbols:")
        
        # Categorize symbols
        major_indices = []
        bond_indices = []
        other_indices = []
        
        for symbol in index_symbols:
            fyers_symbol = symbol.fyers_symbol
            if 'NIFTY' in fyers_symbol or 'SENSEX' in fyers_symbol or 'BANKNIFTY' in fyers_symbol:
                major_indices.append(symbol)
            elif 'BHARATBOND' in fyers_symbol:
                bond_indices.append(symbol)
            else:
                other_indices.append(symbol)
        
        logger.info(f"\n📈 Major Indices ({len(major_indices)}):")
        for symbol in major_indices[:10]:  # Show first 10
            logger.info(f"   {symbol.nse_symbol} -> {symbol.fyers_symbol}")
        
        logger.info(f"\n🏦 Bond Indices ({len(bond_indices)}):")
        for symbol in bond_indices[:5]:  # Show first 5
            logger.info(f"   {symbol.nse_symbol} -> {symbol.fyers_symbol}")
        
        logger.info(f"\n📊 Other Indices ({len(other_indices)}):")
        for symbol in other_indices[:10]:  # Show first 10
            logger.info(f"   {symbol.nse_symbol} -> {symbol.fyers_symbol}")
        
        return major_indices, bond_indices, other_indices
        
    finally:
        db.close()


def test_symbol_data_availability(symbols_to_test: List[str], days_back: int = 5):
    """Test data availability for specific symbols."""
    logger.info(f"\n🧪 Testing data availability for {len(symbols_to_test)} symbols")
    logger.info("=" * 60)
    
    fyers_auth = FyersAuthService()
    if not fyers_auth.initialize():
        logger.error("❌ Failed to initialize Fyers authentication")
        return
    
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days_back)
    
    results = {}
    
    for symbol in symbols_to_test:
        logger.info(f"🔍 Testing {symbol}...")
        try:
            data = fyers_auth.fetch_historical_data_chunked(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                interval=1
            )
            
            data_points = len(data) if data else 0
            results[symbol] = data_points
            
            status = "✅" if data_points > 0 else "❌"
            logger.info(f"   {status} {symbol}: {data_points} data points")
            
        except Exception as e:
            logger.error(f"   ❌ {symbol}: Error - {e}")
            results[symbol] = 0
    
    return results


def add_major_index_symbols():
    """Add major index symbols to symbol_mapping if they don't exist."""
    logger.info("\n➕ Adding major INDEX symbols to symbol_mapping")
    logger.info("=" * 60)
    
    major_symbols = [
        ("NIFTY50", "NSE:NIFTY50-INDEX"),
        ("NIFTY", "NSE:NIFTY50-INDEX"),
        ("BANKNIFTY", "NSE:BANKNIFTY-INDEX"),
        ("SENSEX", "NSE:SENSEX-INDEX"),
        ("NIFTYIT", "NSE:NIFTYIT-INDEX"),
        ("NIFTYPHARMA", "NSE:NIFTYPHARMA-INDEX"),
        ("NIFTYBANK", "NSE:BANKNIFTY-INDEX"),
        ("NIFTYFMCG", "NSE:NIFTYFMCG-INDEX"),
        ("NIFTYAUTO", "NSE:NIFTYAUTO-INDEX"),
        ("NIFTYMETAL", "NSE:NIFTYMETAL-INDEX")
    ]
    
    db = next(get_db())
    try:
        added_count = 0
        for nse_symbol, fyers_symbol in major_symbols:
            # Check if already exists
            existing = db.query(SymbolMapping).filter(
                and_(
                    SymbolMapping.nse_symbol == nse_symbol,
                    SymbolMapping.market_type == MarketType.INDEX
                )
            ).first()
            
            if not existing:
                new_mapping = SymbolMapping(
                    nse_symbol=nse_symbol,
                    fyers_symbol=fyers_symbol,
                    market_type=MarketType.INDEX,
                    exchange='NSE',
                    is_active=True
                )
                db.add(new_mapping)
                added_count += 1
                logger.info(f"   ➕ Added: {nse_symbol} -> {fyers_symbol}")
            else:
                logger.info(f"   ✅ Exists: {nse_symbol} -> {existing.fyers_symbol}")
        
        if added_count > 0:
            db.commit()
            logger.info(f"✅ Added {added_count} new major index symbols")
        else:
            logger.info("✅ All major index symbols already exist")
            
    except Exception as e:
        logger.error(f"❌ Error adding symbols: {e}")
        db.rollback()
    finally:
        db.close()


def main():
    """Main function."""
    logger.info("🚀 Symbol Mapping Debug Script")
    logger.info("=" * 80)
    
    # 1. Check existing symbols
    major_indices, bond_indices, other_indices = check_index_symbols()
    
    # 2. Add major index symbols if needed
    add_major_index_symbols()
    
    # 3. Test data availability for major indices
    test_symbols = [
        "NSE:NIFTY50-INDEX",
        "NSE:BANKNIFTY-INDEX",
        "NSE:SENSEX-INDEX",
        "NSE:NIFTYIT-INDEX"
    ]
    
    if major_indices:
        # Add some existing major indices to test
        test_symbols.extend([symbol.fyers_symbol for symbol in major_indices[:3]])
    
    # Remove duplicates
    test_symbols = list(set(test_symbols))
    
    results = test_symbol_data_availability(test_symbols)
    
    # 4. Summary
    logger.info("\n📊 SUMMARY")
    logger.info("=" * 40)
    working_symbols = [symbol for symbol, count in results.items() if count > 0]
    failed_symbols = [symbol for symbol, count in results.items() if count == 0]
    
    logger.info(f"✅ Working symbols ({len(working_symbols)}):")
    for symbol in working_symbols:
        logger.info(f"   {symbol}")
    
    logger.info(f"\n❌ Failed symbols ({len(failed_symbols)}):")
    for symbol in failed_symbols:
        logger.info(f"   {symbol}")
    
    if working_symbols:
        logger.info(f"\n💡 RECOMMENDATION:")
        logger.info(f"   Use these working symbols for testing auto-all-symbols:")
        logger.info(f"   The issue is that bond indices and some other indices")
        logger.info(f"   may not have data for the specific date range.")


if __name__ == "__main__":
    main()
