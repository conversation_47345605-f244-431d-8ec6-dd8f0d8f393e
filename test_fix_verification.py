#!/usr/bin/env python3
"""
Simple verification test for the auto-all-symbols fix.
"""

import subprocess
import sys
import time
from datetime import datetime

def run_test(command, test_name, timeout=60):
    """Run a test command and return results."""
    print(f"\n🧪 Testing: {test_name}")
    print(f"📝 Command: {' '.join(command)}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Check for success indicators
        output = result.stdout
        success = result.returncode == 0 and ("100.0% success" in output or "symbols successful" in output)
        
        # Extract metrics
        symbols_processed = 0
        symbols_successful = 0
        
        for line in output.split('\n'):
            if "Total symbols processed:" in line:
                try:
                    symbols_processed = int(line.split(':')[1].strip())
                except:
                    pass
            elif "Total successful:" in line:
                try:
                    symbols_successful = int(line.split(':')[1].strip())
                except:
                    pass
            elif "Successful:" in line and "/" in line:
                try:
                    # Parse "Successful: 5/5 (100.0%)" format
                    parts = line.split("Successful:")[1].strip().split("/")
                    symbols_successful = int(parts[0].strip())
                    symbols_processed = int(parts[1].split()[0].strip())
                except:
                    pass
        
        if success:
            print(f"✅ SUCCESS: {symbols_successful}/{symbols_processed} symbols in {execution_time:.1f}s")
        else:
            print(f"❌ FAILED: Return code {result.returncode}")
            if result.stderr:
                print(f"   Error: {result.stderr[:200]}")
        
        return {
            'success': success,
            'symbols_processed': symbols_processed,
            'symbols_successful': symbols_successful,
            'execution_time': execution_time
        }
        
    except subprocess.TimeoutExpired:
        print(f"⏰ TIMEOUT after {timeout}s")
        return {'success': False, 'error': 'timeout'}
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return {'success': False, 'error': str(e)}


def main():
    """Run verification tests."""
    print("🚀 Auto-All-Symbols Fix Verification")
    print("=" * 50)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test cases
    tests = [
        {
            'name': 'INDEX - Small Test (3 symbols)',
            'command': ['python', 'main.py', '--auto-all-symbols', '--market-type', 'INDEX', '--days', '2', '--limit', '3'],
            'timeout': 60
        },
        {
            'name': 'INDEX - Medium Test (10 symbols)',
            'command': ['python', 'main.py', '--auto-all-symbols', '--market-type', 'INDEX', '--days', '2', '--limit', '10'],
            'timeout': 120
        },
        {
            'name': 'EQUITY - Small Test (3 symbols)',
            'command': ['python', 'main.py', '--auto-all-symbols', '--market-type', 'EQUITY', '--days', '2', '--limit', '3'],
            'timeout': 60
        },
        {
            'name': 'FUTURES - Small Test (3 symbols)',
            'command': ['python', 'main.py', '--auto-all-symbols', '--market-type', 'FUTURES', '--days', '2', '--limit', '3'],
            'timeout': 60
        }
    ]
    
    results = []
    total_start_time = time.time()
    
    for test in tests:
        result = run_test(test['command'], test['name'], test['timeout'])
        result['test_name'] = test['name']
        results.append(result)
    
    total_time = time.time() - total_start_time
    
    # Summary
    print("\n📊 SUMMARY REPORT")
    print("=" * 40)
    
    successful_tests = sum(1 for r in results if r.get('success', False))
    total_tests = len(results)
    total_symbols_processed = sum(r.get('symbols_processed', 0) for r in results)
    total_symbols_successful = sum(r.get('symbols_successful', 0) for r in results)
    
    print(f"📈 Test Success Rate: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
    print(f"📊 Symbol Success Rate: {total_symbols_successful}/{total_symbols_processed} ({total_symbols_successful/total_symbols_processed*100:.1f}%)")
    print(f"⏱️  Total Time: {total_time:.1f}s")
    
    if total_symbols_processed > 0:
        print(f"⚡ Average Time per Symbol: {total_time/total_symbols_processed:.2f}s")
    
    # Individual results
    print("\n📋 Individual Test Results:")
    for result in results:
        status = "✅" if result.get('success', False) else "❌"
        symbols = f"{result.get('symbols_successful', 0)}/{result.get('symbols_processed', 0)}"
        time_taken = result.get('execution_time', 0)
        print(f"   {status} {result['test_name']}: {symbols} symbols ({time_taken:.1f}s)")
    
    # Final assessment
    if successful_tests == total_tests and total_symbols_successful > 0:
        print("\n🎉 EXCELLENT: All tests passed! Auto-all-symbols is working perfectly!")
    elif successful_tests >= total_tests * 0.75:
        print("\n✅ GOOD: Most tests passed! Auto-all-symbols is working well!")
    else:
        print("\n⚠️ NEEDS ATTENTION: Some tests failed. Check the issues above.")
    
    print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()
