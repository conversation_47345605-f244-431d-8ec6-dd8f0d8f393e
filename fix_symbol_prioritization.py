#!/usr/bin/env python3
"""
Fix script to prioritize working symbols and filter out problematic ones.
This will improve the auto-all-symbols functionality.
"""

import sys
import os
from datetime import datetime
from typing import List, Dict, Any

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.logging import get_logger, setup_enhanced_logging
from src.database.connection import get_db
from src.database.models import SymbolMapping, MarketType
from sqlalchemy import and_, update

# Setup logging
setup_enhanced_logging()
logger = get_logger(__name__)


class SymbolPrioritizer:
    """Fix symbol prioritization for auto-all-symbols functionality."""
    
    def __init__(self):
        self.db = next(get_db())
        
        # Define symbol priorities and filters
        self.priority_patterns = {
            MarketType.INDEX: {
                'high_priority': ['NIFTY50', 'NIFTY100', 'FINNIFTY', 'MIDCPNIFTY', 'NIFTYIT', 'NIFTYPHARMA', 'NIFTYAUTO', 'NIFTYMETAL', 'NIFTYFMCG'],
                'medium_priority': ['NIFTY'],
                'exclude_patterns': ['BHARATBOND', 'DJIA', 'FTSE', 'S&P', 'HANGSENG']
            },
            MarketType.EQUITY: {
                'high_priority': ['RELIANCE', 'TCS', 'INFY', 'HDFCBANK', 'ICICIBANK', 'SBIN', 'BHARTIARTL', 'ITC', 'KOTAKBANK'],
                'medium_priority': ['HDFC', 'WIPRO', 'ONGC', 'NTPC', 'POWERGRID'],
                'exclude_patterns': []
            },
            MarketType.FUTURES: {
                'high_priority': ['RELIANCE', 'NIFTY', 'BANKNIFTY', 'TCS', 'INFY'],
                'medium_priority': ['HDFC', 'ICICI', 'SBI'],
                'exclude_patterns': []
            },
            MarketType.OPTIONS: {
                'high_priority': ['NIFTY', 'BANKNIFTY', 'RELIANCE', 'TCS'],
                'medium_priority': ['HDFC', 'ICICI'],
                'exclude_patterns': []
            }
        }
    
    def __del__(self):
        if hasattr(self, 'db'):
            self.db.close()
    
    def add_priority_column(self):
        """Add priority column to symbol_mapping table if it doesn't exist."""
        try:
            # Check if priority column exists
            result = self.db.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'symbol_mapping' AND column_name = 'priority'
            """).fetchone()
            
            if not result:
                logger.info("➕ Adding priority column to symbol_mapping table...")
                self.db.execute("ALTER TABLE symbol_mapping ADD COLUMN priority INTEGER DEFAULT 50")
                self.db.commit()
                logger.info("✅ Priority column added successfully")
            else:
                logger.info("✅ Priority column already exists")
                
        except Exception as e:
            logger.error(f"❌ Error adding priority column: {e}")
            self.db.rollback()
    
    def update_symbol_priorities(self, market_type: MarketType):
        """Update symbol priorities for a specific market type."""
        try:
            logger.info(f"🔄 Updating priorities for {market_type.value} symbols...")
            
            patterns = self.priority_patterns.get(market_type, {})
            high_priority = patterns.get('high_priority', [])
            medium_priority = patterns.get('medium_priority', [])
            exclude_patterns = patterns.get('exclude_patterns', [])
            
            # Get all symbols for this market type
            symbols = self.db.query(SymbolMapping).filter(
                and_(
                    SymbolMapping.market_type == market_type,
                    SymbolMapping.is_active == True
                )
            ).all()
            
            updated_count = 0
            excluded_count = 0
            
            for symbol in symbols:
                nse_symbol = symbol.nse_symbol
                new_priority = 50  # Default priority
                
                # Check for exclusion patterns
                should_exclude = any(pattern in nse_symbol.upper() for pattern in exclude_patterns)
                if should_exclude:
                    new_priority = 90  # Low priority (processed last)
                    excluded_count += 1
                    logger.info(f"   📉 Low priority: {nse_symbol} (excluded pattern)")
                else:
                    # Check for high priority patterns
                    is_high_priority = any(pattern in nse_symbol.upper() for pattern in high_priority)
                    if is_high_priority:
                        new_priority = 10  # High priority
                        logger.info(f"   📈 High priority: {nse_symbol}")
                    else:
                        # Check for medium priority patterns
                        is_medium_priority = any(pattern in nse_symbol.upper() for pattern in medium_priority)
                        if is_medium_priority:
                            new_priority = 30  # Medium priority
                            logger.info(f"   📊 Medium priority: {nse_symbol}")
                
                # Update priority if different
                if hasattr(symbol, 'priority') and symbol.priority != new_priority:
                    symbol.priority = new_priority
                    updated_count += 1
                elif not hasattr(symbol, 'priority'):
                    # If priority column doesn't exist in the model, use raw SQL
                    self.db.execute(
                        update(SymbolMapping)
                        .where(SymbolMapping.id == symbol.id)
                        .values(priority=new_priority)
                    )
                    updated_count += 1
            
            self.db.commit()
            
            logger.info(f"✅ Updated {updated_count} symbols for {market_type.value}")
            logger.info(f"📉 Marked {excluded_count} symbols as low priority")
            
            return updated_count
            
        except Exception as e:
            logger.error(f"❌ Error updating priorities for {market_type.value}: {e}")
            self.db.rollback()
            return 0
    
    def create_optimized_symbol_query_method(self):
        """Create an optimized method for getting symbols with priority ordering."""
        
        method_code = '''
    def _get_symbols_from_mapping_prioritized(self, market_type: str, limit: Optional[int] = None) -> List[str]:
        """Get Fyers symbols from symbol_mapping table with priority ordering."""
        try:
            from src.database.connection import get_db
            from src.database.models import SymbolMapping, MarketType
            from sqlalchemy import and_, text
            
            db = next(get_db())
            
            try:
                market_type_enum = MarketType(market_type.upper())
                
                # Check if priority column exists
                has_priority = db.execute(text("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'symbol_mapping' AND column_name = 'priority'
                """)).fetchone()
                
                query = db.query(SymbolMapping).filter(
                    and_(
                        SymbolMapping.market_type == market_type_enum,
                        SymbolMapping.is_active == True,
                        SymbolMapping.fyers_symbol.isnot(None)
                    )
                )
                
                # Order by priority if column exists, otherwise by nse_symbol
                if has_priority:
                    query = query.order_by(text('priority ASC, nse_symbol ASC'))
                else:
                    # Fallback: prioritize NIFTY symbols for INDEX market type
                    if market_type_enum == MarketType.INDEX:
                        query = query.order_by(
                            text("CASE WHEN nse_symbol LIKE '%NIFTY%' THEN 1 ELSE 2 END, nse_symbol ASC")
                        )
                    else:
                        query = query.order_by(SymbolMapping.nse_symbol)
                
                if limit:
                    query = query.limit(limit)
                
                mappings = query.all()
                return [mapping.fyers_symbol for mapping in mappings]
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error getting prioritized symbols from mapping: {e}")
            return []
        '''
        
        return method_code
    
    def fix_all_market_types(self):
        """Fix symbol priorities for all market types."""
        logger.info("🚀 Fixing symbol priorities for all market types")
        logger.info("=" * 60)
        
        # Add priority column
        self.add_priority_column()
        
        total_updated = 0
        for market_type in [MarketType.INDEX, MarketType.EQUITY, MarketType.FUTURES, MarketType.OPTIONS]:
            updated = self.update_symbol_priorities(market_type)
            total_updated += updated
        
        logger.info(f"\n✅ Total symbols updated: {total_updated}")
        
        # Show the optimized query method
        logger.info("\n💡 Optimized query method:")
        logger.info("=" * 40)
        logger.info("Add this method to cli_operations.py to use prioritized symbol ordering:")
        print(self.create_optimized_symbol_query_method())
    
    def test_prioritized_symbols(self, market_type: MarketType, limit: int = 10):
        """Test the prioritized symbol ordering."""
        try:
            logger.info(f"\n🧪 Testing prioritized symbols for {market_type.value} (limit: {limit})")
            logger.info("=" * 60)
            
            # Check if priority column exists
            has_priority = self.db.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'symbol_mapping' AND column_name = 'priority'
            """).fetchone()
            
            if has_priority:
                symbols = self.db.execute(f"""
                    SELECT nse_symbol, fyers_symbol, priority 
                    FROM symbol_mapping 
                    WHERE market_type = '{market_type.value}' 
                    AND is_active = true 
                    AND fyers_symbol IS NOT NULL
                    ORDER BY priority ASC, nse_symbol ASC
                    LIMIT {limit}
                """).fetchall()
                
                logger.info("📊 Prioritized symbols (priority, nse_symbol, fyers_symbol):")
                for i, (nse_symbol, fyers_symbol, priority) in enumerate(symbols, 1):
                    logger.info(f"   {i:2d}. Priority {priority:2d}: {nse_symbol} -> {fyers_symbol}")
            else:
                logger.warning("⚠️ Priority column not found, showing default ordering")
                
        except Exception as e:
            logger.error(f"❌ Error testing prioritized symbols: {e}")


def main():
    """Main function."""
    logger.info("🔧 Symbol Prioritization Fix")
    logger.info("=" * 80)
    
    prioritizer = SymbolPrioritizer()
    
    # Fix all market types
    prioritizer.fix_all_market_types()
    
    # Test the results
    for market_type in [MarketType.INDEX, MarketType.EQUITY]:
        prioritizer.test_prioritized_symbols(market_type, 10)


if __name__ == "__main__":
    main()
