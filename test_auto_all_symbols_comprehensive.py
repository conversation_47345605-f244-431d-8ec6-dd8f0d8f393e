#!/usr/bin/env python3
"""
Comprehensive test suite for auto-all-symbols functionality across all market types.
"""

import sys
import os
import subprocess
import time
from datetime import datetime
from typing import Dict, List, Any

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.logging import get_logger, setup_enhanced_logging

# Setup logging
setup_enhanced_logging()
logger = get_logger(__name__)


class AutoAllSymbolsTestSuite:
    """Comprehensive test suite for auto-all-symbols functionality."""
    
    def __init__(self):
        self.test_results = {}
        self.performance_metrics = {}
    
    def run_command_test(self, command: List[str], test_name: str, timeout: int = 120) -> Dict[str, Any]:
        """Run a command and capture results."""
        logger.info(f"🧪 Running test: {test_name}")
        logger.info(f"📝 Command: {' '.join(command)}")
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=os.getcwd()
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Parse output for success indicators
            output = result.stdout
            success = result.returncode == 0 and "100.0% success" in output
            
            # Extract metrics from output
            symbols_processed = 0
            symbols_successful = 0
            
            for line in output.split('\n'):
                if "Total symbols processed:" in line:
                    symbols_processed = int(line.split(':')[1].strip())
                elif "Total successful:" in line:
                    symbols_successful = int(line.split(':')[1].strip())
            
            test_result = {
                'success': success,
                'return_code': result.returncode,
                'execution_time': execution_time,
                'symbols_processed': symbols_processed,
                'symbols_successful': symbols_successful,
                'output_lines': len(output.split('\n')),
                'error_output': result.stderr if result.stderr else None
            }
            
            if success:
                logger.info(f"✅ {test_name}: SUCCESS ({symbols_successful}/{symbols_processed} symbols, {execution_time:.1f}s)")
            else:
                logger.error(f"❌ {test_name}: FAILED (return code: {result.returncode})")
                if result.stderr:
                    logger.error(f"   Error: {result.stderr[:200]}...")
            
            return test_result
            
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ {test_name}: TIMEOUT after {timeout}s")
            return {
                'success': False,
                'return_code': -1,
                'execution_time': timeout,
                'error': 'Timeout'
            }
        except Exception as e:
            logger.error(f"❌ {test_name}: EXCEPTION - {e}")
            return {
                'success': False,
                'return_code': -1,
                'execution_time': 0,
                'error': str(e)
            }
    
    def test_index_market_type(self) -> Dict[str, Any]:
        """Test INDEX market type with various configurations."""
        logger.info("\n📊 Testing INDEX Market Type")
        logger.info("=" * 50)
        
        tests = {
            'index_small': ['python', 'main.py', '--auto-all-symbols', '--market-type', 'INDEX', '--days', '2', '--limit', '3'],
            'index_medium': ['python', 'main.py', '--auto-all-symbols', '--market-type', 'INDEX', '--days', '2', '--limit', '10'],
            'index_date_range': ['python', 'main.py', '--auto-all-symbols', '--market-type', 'INDEX', '--start-date', '2024-07-25', '--end-date', '2024-07-26', '--limit', '5']
        }
        
        results = {}
        for test_name, command in tests.items():
            results[test_name] = self.run_command_test(command, test_name, 120)
        
        return results
    
    def test_equity_market_type(self) -> Dict[str, Any]:
        """Test EQUITY market type."""
        logger.info("\n📈 Testing EQUITY Market Type")
        logger.info("=" * 50)
        
        tests = {
            'equity_small': ['python', 'main.py', '--auto-all-symbols', '--market-type', 'EQUITY', '--days', '2', '--limit', '3'],
            'equity_medium': ['python', 'main.py', '--auto-all-symbols', '--market-type', 'EQUITY', '--days', '2', '--limit', '8']
        }
        
        results = {}
        for test_name, command in tests.items():
            results[test_name] = self.run_command_test(command, test_name, 90)
        
        return results
    
    def test_futures_market_type(self) -> Dict[str, Any]:
        """Test FUTURES market type."""
        logger.info("\n🔮 Testing FUTURES Market Type")
        logger.info("=" * 50)
        
        tests = {
            'futures_small': ['python', 'main.py', '--auto-all-symbols', '--market-type', 'FUTURES', '--days', '2', '--limit', '3'],
            'futures_medium': ['python', 'main.py', '--auto-all-symbols', '--market-type', 'FUTURES', '--days', '2', '--limit', '6']
        }
        
        results = {}
        for test_name, command in tests.items():
            results[test_name] = self.run_command_test(command, test_name, 90)
        
        return results
    
    def test_options_market_type(self) -> Dict[str, Any]:
        """Test OPTIONS market type (may have limited success due to expiry dates)."""
        logger.info("\n📊 Testing OPTIONS Market Type")
        logger.info("=" * 50)
        
        tests = {
            'options_small': ['python', 'main.py', '--auto-all-symbols', '--market-type', 'OPTIONS', '--days', '2', '--limit', '3']
        }
        
        results = {}
        for test_name, command in tests.items():
            results[test_name] = self.run_command_test(command, test_name, 90)
        
        return results
    
    def test_performance_scaling(self) -> Dict[str, Any]:
        """Test performance with different symbol counts."""
        logger.info("\n⚡ Testing Performance Scaling")
        logger.info("=" * 50)
        
        tests = {
            'perf_5_symbols': ['python', 'main.py', '--auto-all-symbols', '--market-type', 'INDEX', '--days', '2', '--limit', '5'],
            'perf_15_symbols': ['python', 'main.py', '--auto-all-symbols', '--market-type', 'INDEX', '--days', '2', '--limit', '15'],
            'perf_25_symbols': ['python', 'main.py', '--auto-all-symbols', '--market-type', 'INDEX', '--days', '2', '--limit', '25']
        }
        
        results = {}
        for test_name, command in tests.items():
            results[test_name] = self.run_command_test(command, test_name, 180)
        
        return results
    
    def run_comprehensive_test_suite(self) -> Dict[str, Any]:
        """Run the complete test suite."""
        logger.info("🚀 Starting Comprehensive Auto-All-Symbols Test Suite")
        logger.info("=" * 80)
        logger.info(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        suite_start_time = time.time()
        
        # Run all test categories
        all_results = {
            'index_tests': self.test_index_market_type(),
            'equity_tests': self.test_equity_market_type(),
            'futures_tests': self.test_futures_market_type(),
            'options_tests': self.test_options_market_type(),
            'performance_tests': self.test_performance_scaling()
        }
        
        suite_end_time = time.time()
        total_time = suite_end_time - suite_start_time
        
        # Generate summary report
        self.generate_summary_report(all_results, total_time)
        
        return all_results
    
    def generate_summary_report(self, results: Dict[str, Any], total_time: float):
        """Generate a comprehensive summary report."""
        logger.info("\n📊 COMPREHENSIVE TEST SUMMARY REPORT")
        logger.info("=" * 80)
        
        total_tests = 0
        successful_tests = 0
        total_symbols_processed = 0
        total_symbols_successful = 0
        
        for category, tests in results.items():
            logger.info(f"\n📋 {category.upper().replace('_', ' ')}:")
            category_success = 0
            category_total = 0
            
            for test_name, result in tests.items():
                total_tests += 1
                category_total += 1
                
                if result.get('success', False):
                    successful_tests += 1
                    category_success += 1
                    status = "✅"
                else:
                    status = "❌"
                
                symbols_processed = result.get('symbols_processed', 0)
                symbols_successful = result.get('symbols_successful', 0)
                execution_time = result.get('execution_time', 0)
                
                total_symbols_processed += symbols_processed
                total_symbols_successful += symbols_successful
                
                logger.info(f"   {status} {test_name}: {symbols_successful}/{symbols_processed} symbols ({execution_time:.1f}s)")
            
            success_rate = (category_success / category_total * 100) if category_total > 0 else 0
            logger.info(f"   📊 Category Success Rate: {success_rate:.1f}% ({category_success}/{category_total})")
        
        # Overall statistics
        overall_success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        symbol_success_rate = (total_symbols_successful / total_symbols_processed * 100) if total_symbols_processed > 0 else 0
        
        logger.info(f"\n🎯 OVERALL RESULTS:")
        logger.info(f"   📊 Test Success Rate: {overall_success_rate:.1f}% ({successful_tests}/{total_tests})")
        logger.info(f"   📈 Symbol Success Rate: {symbol_success_rate:.1f}% ({total_symbols_successful}/{total_symbols_processed})")
        logger.info(f"   ⏱️  Total Execution Time: {total_time:.1f} seconds")
        logger.info(f"   ⚡ Average Time per Symbol: {total_time/total_symbols_processed:.2f}s")
        
        # Performance insights
        if symbol_success_rate >= 90:
            logger.info("🎉 EXCELLENT: Auto-all-symbols functionality is working very well!")
        elif symbol_success_rate >= 70:
            logger.info("✅ GOOD: Auto-all-symbols functionality is working well with minor issues")
        else:
            logger.info("⚠️ NEEDS IMPROVEMENT: Auto-all-symbols functionality needs attention")


def main():
    """Main function to run the test suite."""
    test_suite = AutoAllSymbolsTestSuite()
    results = test_suite.run_comprehensive_test_suite()
    
    logger.info(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("🏁 Test suite completed!")


if __name__ == "__main__":
    main()
