"""
Handler for data fetching operations.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from src.handlers.base_handler import <PERSON><PERSON>ommandHandler
from src.helpers.cli_operations import CLIOperations
from src.services.bulk_data_service import BulkDataService
from src.core.symbol_classifier import SymbolClassifier
from src.database.models import MarketType
from src.core.logging import get_logger

logger = get_logger(__name__)


class DataFetchHandler(BaseCommandHandler):
    """Handler for all data fetching operations."""
    
    def __init__(self):
        super().__init__()
        self.cli_ops = CLIOperations()
        self.bulk_service = BulkDataService()
        self.classifier = SymbolClassifier()
    
    def execute(self, args: Any) -> bool:
        """Execute data fetching based on arguments."""
        try:
            if args.fetch_equity or args.fetch_index or args.fetch_futures or args.fetch_options:
                return self._handle_specific_symbol_fetch(args)
            elif args.auto_all_symbols:
                return self._handle_auto_all_symbols(args)
            elif args.fetch_data:
                return self._handle_fetch_data(args)
            elif args.bulk_all_markets:
                return self._handle_bulk_all_markets(args)
            else:
                self.logger.error("❌ No valid data fetch operation specified")
                return False
                
        except Exception as e:
            return self.handle_error(e, "Data fetch operation")
    
    def _handle_specific_symbol_fetch(self, args: Any) -> bool:
        """Handle specific symbol fetch operations."""
        try:
            symbol = None
            market_type = None
            
            if args.fetch_equity:
                symbol = args.fetch_equity
                market_type = "EQUITY"
            elif args.fetch_index:
                symbol = args.fetch_index
                market_type = "INDEX"
            elif args.fetch_futures:
                symbol = args.fetch_futures
                market_type = "FUTURES"
            elif args.fetch_options:
                symbol = args.fetch_options
                market_type = "OPTIONS"
            
            if symbol and market_type:
                success = self.cli_ops.fetch_specific_symbol_data(symbol, market_type, args.days)
                if success:
                    return self.log_success(f"Successfully fetched data for {symbol}")
                else:
                    self.logger.error(f"❌ Failed to fetch data for {symbol}")
                    return False
            else:
                self.logger.error("❌ No valid symbol and market type specified")
                return False
                
        except Exception as e:
            return self.handle_error(e, "Specific symbol fetch")
    
    def _handle_auto_all_symbols(self, args: Any) -> bool:
        """Handle auto all symbols operation with improved error handling."""
        try:
            market_type = args.market_type or "EQUITY"

            # Parse date arguments with validation and adjustment
            start_date, end_date = self._parse_date_arguments(args)

            # Validate date range (warnings only)
            self._validate_date_range(start_date, end_date)

            self.logger.info(f"📅 Final date range for processing: {start_date.date()} to {end_date.date()}")

            # Process symbols with resume capability and date range
            results = self.cli_ops.process_symbols_with_resume_and_dates(
                market_type=market_type,
                start_date=start_date,
                end_date=end_date,
                batch_size=10,
                start_from=args.resume_from,
                limit=args.limit
            )

            if results['success']:
                return self.log_success(f"Successfully processed {results['successful_symbols']} symbols")
            else:
                self.logger.error(f"❌ Processing failed: {results.get('error', 'Unknown error')}")
                return False

        except Exception as e:
            return self.handle_error(e, "Auto all symbols processing")
    
    def _handle_fetch_data(self, args: Any) -> bool:
        """Handle fetch data for specific symbols."""
        try:
            if not args.symbols:
                self.logger.error("❌ --fetch-data requires --symbols to be specified")
                return False
            
            self.logger.info(f"🚀 Fetching data for symbols: {', '.join(args.symbols)}")
            self.logger.info(f"📅 Days: {args.days}")
            
            # Classify symbols by market type
            classified_symbols = self.classifier.classify_symbols_batch(args.symbols)
            
            # Log classification results
            self.logger.info("📊 Symbol classification:")
            for market_type, symbols in classified_symbols.items():
                if symbols:
                    symbol_names = [s['symbol'] for s in symbols]
                    self.logger.info(f"  {market_type.value}: {symbol_names}")
            
            # Convert to the format expected by BulkDataService
            symbols_config = {}
            for market_type, symbols in classified_symbols.items():
                if symbols:
                    symbols_config[market_type] = [s['symbol'] for s in symbols]
            
            if not symbols_config:
                self.logger.error("❌ No symbols could be classified. Please check symbol formats.")
                self._show_symbol_format_help()
                return False
            
            # Use bulk service for fetching data
            results = asyncio.run(self.bulk_service.populate_all_market_types(
                symbols_config=symbols_config,
                days=args.days
            ))
            
            return self._process_bulk_results(results)
            
        except Exception as e:
            return self.handle_error(e, "Fetch data operation")
    
    def _handle_bulk_all_markets(self, args: Any) -> bool:
        """Handle bulk all markets operation."""
        try:
            self.logger.info(f"🚀 Starting bulk insert for ALL MARKET TYPES with {args.days} days of data")
            
            # Process NSE symbols first
            from src.core.nse_symbol_processor import NSESymbolProcessor
            processor = NSESymbolProcessor()
            
            try:
                nse_results = processor.process_nse_files()
                if all(nse_results.values()):
                    self.logger.info("✅ NSE symbol processing completed successfully")
                    sample_symbols = processor.get_sample_symbols_by_type()
                else:
                    self.logger.warning("⚠️ NSE symbol processing had issues, proceeding with default symbols")
                    sample_symbols = {}
            except Exception as e:
                self.logger.warning(f"⚠️ NSE symbol processing failed: {e}, proceeding with default symbols")
                sample_symbols = {}
            
            # Define symbols for each market type
            symbols_config = {
                MarketType.EQUITY: [sample_symbols.get('EQUITY', 'RELIANCE')],
                MarketType.INDEX: [sample_symbols.get('INDEX', 'NIFTY50')],
                MarketType.FUTURES: [sample_symbols.get('FUTURES', 'RELIANCE')],
                MarketType.OPTIONS: [sample_symbols.get('OPTIONS', 'RELIANCE')]
            }
            
            # Override with specific symbols if provided
            if args.symbols:
                for market_type in symbols_config:
                    symbols_config[market_type] = args.symbols
            
            # Run bulk population for all market types
            all_results = asyncio.run(self.bulk_service.populate_all_market_types(
                symbols_config=symbols_config,
                days=args.days
            ))
            
            return self._process_bulk_results(all_results)
            
        except Exception as e:
            return self.handle_error(e, "Bulk all markets operation")
    
    def _parse_date_arguments(self, args: Any) -> tuple[datetime, datetime]:
        """Parse start and end date arguments, with fallback to days argument."""
        now = datetime.now()

        if args.start_date and args.end_date:
            try:
                start_date = datetime.strptime(args.start_date, "%Y-%m-%d").replace(hour=0, minute=0, second=0, microsecond=0)
                end_date = datetime.strptime(args.end_date, "%Y-%m-%d").replace(hour=23, minute=59, second=59, microsecond=999999)

                if start_date >= end_date:
                    raise ValueError("Start date must be before end date")

                # Adjust future dates to today
                if end_date.date() > now.date():
                    self.logger.warning(f"⚠️  End date {end_date.date()} is in the future. Adjusting to today.")
                    end_date = now.replace(hour=23, minute=59, second=59, microsecond=999999)

                if start_date.date() > now.date():
                    self.logger.warning(f"⚠️  Start date {start_date.date()} is in the future. Adjusting to yesterday.")
                    start_date = (now - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)

                # Ensure we have at least 1 day of data
                if (end_date - start_date).days < 1:
                    self.logger.warning(f"⚠️  Date range too small. Adjusting to get at least 1 day of data.")
                    start_date = end_date - timedelta(days=1)

                self.logger.info(f"📅 Using date range: {start_date.date()} to {end_date.date()}")
                return start_date, end_date

            except ValueError as e:
                self.logger.error(f"❌ Invalid date format: {e}")
                self.logger.error("Please use YYYY-MM-DD format for dates")
                raise
        else:
            # Fallback to days argument
            end_date = now.replace(hour=23, minute=59, second=59, microsecond=999999)
            start_date = end_date - timedelta(days=args.days)
            self.logger.info(f"📅 Using {args.days} days range: {start_date.date()} to {end_date.date()}")
            return start_date, end_date
    
    def _validate_date_range(self, start_date: datetime, end_date: datetime) -> bool:
        """Validate that the date range is reasonable for data fetching."""
        now = datetime.now()

        # Check if end date is in the future
        if end_date.date() > now.date():
            self.logger.warning(f"⚠️  End date {end_date.date()} is in the future. This may result in no data.")
            # Don't return False, just warn - let the API handle it

        # Check if the date range is too small
        if (end_date - start_date).days < 1:
            self.logger.warning(f"⚠️  Date range is less than 1 day. This may result in no data.")
            # Don't return False, just warn - some intraday data might still be available

        # Check if dates are too far in the past (more than 5 years)
        five_years_ago = now - timedelta(days=5*365)
        if start_date < five_years_ago:
            self.logger.warning(f"⚠️  Start date {start_date.date()} is more than 5 years ago. Data may not be available.")

        return True
    
    def _process_bulk_results(self, results: Dict) -> bool:
        """Process and log bulk operation results."""
        overall_success = True
        
        for market_type, symbol_results in results.items():
            successful_symbols = [s for s, success in symbol_results.items() if success]
            failed_symbols = [s for s, success in symbol_results.items() if not success]
            
            if successful_symbols:
                self.logger.info(f"✅ {market_type.value}: Successfully fetched data for {successful_symbols}")
            if failed_symbols:
                self.logger.error(f"❌ {market_type.value}: Failed to fetch data for {failed_symbols}")
                overall_success = False
        
        if overall_success:
            return self.log_success("Data fetch completed successfully")
        else:
            self.logger.error("❌ Some data fetch operations failed")
            return False
    
    def _show_symbol_format_help(self) -> None:
        """Show help for symbol formats."""
        self.logger.info("💡 Expected formats:")
        self.logger.info("   EQUITY: RELIANCE-EQ")
        self.logger.info("   INDEX: NIFTY50-INDEX")
        self.logger.info("   FUTURES: RELIANCE25JULFUT")
        self.logger.info("   OPTIONS: NIFTY25JUL25000CE")
