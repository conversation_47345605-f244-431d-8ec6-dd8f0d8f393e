"""
Performance optimization utilities for the data service.
"""

import time
import asyncio
from functools import wraps, lru_cache
from typing import Any, Callable, Dict, List, Optional
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading
from datetime import datetime, timedelta

from src.core.logging import get_logger

logger = get_logger(__name__)


class PerformanceOptimizer:
    """Performance optimization utilities."""
    
    def __init__(self):
        self.cache = {}
        self.cache_ttl = {}
        self.lock = threading.Lock()
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
    
    def cached_result(self, ttl_seconds: int = 300):
        """Decorator for caching function results with TTL."""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Create cache key
                cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
                
                with self.lock:
                    # Check if cached result exists and is still valid
                    if cache_key in self.cache:
                        cached_time = self.cache_ttl.get(cache_key, 0)
                        if time.time() - cached_time < ttl_seconds:
                            logger.debug(f"Cache hit for {func.__name__}")
                            return self.cache[cache_key]
                    
                    # Execute function and cache result
                    logger.debug(f"Cache miss for {func.__name__}, executing...")
                    result = func(*args, **kwargs)
                    self.cache[cache_key] = result
                    self.cache_ttl[cache_key] = time.time()
                    
                    return result
            return wrapper
        return decorator
    
    def clear_cache(self, pattern: Optional[str] = None):
        """Clear cache entries, optionally matching a pattern."""
        with self.lock:
            if pattern:
                keys_to_remove = [k for k in self.cache.keys() if pattern in k]
                for key in keys_to_remove:
                    del self.cache[key]
                    if key in self.cache_ttl:
                        del self.cache_ttl[key]
                logger.info(f"Cleared {len(keys_to_remove)} cache entries matching '{pattern}'")
            else:
                self.cache.clear()
                self.cache_ttl.clear()
                logger.info("Cleared all cache entries")
    
    def batch_process(self, items: List[Any], process_func: Callable, batch_size: int = 10, 
                     max_workers: int = 4) -> List[Any]:
        """Process items in batches with parallel execution."""
        results = []
        
        # Split items into batches
        batches = [items[i:i + batch_size] for i in range(0, len(items), batch_size)]
        
        logger.info(f"Processing {len(items)} items in {len(batches)} batches with {max_workers} workers")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all batches
            future_to_batch = {
                executor.submit(self._process_batch, batch, process_func): batch 
                for batch in batches
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_batch):
                batch = future_to_batch[future]
                try:
                    batch_results = future.result()
                    results.extend(batch_results)
                except Exception as e:
                    logger.error(f"Batch processing failed: {e}")
                    # Add None results for failed batch
                    results.extend([None] * len(batch))
        
        return results
    
    def _process_batch(self, batch: List[Any], process_func: Callable) -> List[Any]:
        """Process a single batch of items."""
        results = []
        for item in batch:
            try:
                result = process_func(item)
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to process item {item}: {e}")
                results.append(None)
        return results
    
    async def async_batch_process(self, items: List[Any], async_process_func: Callable, 
                                 batch_size: int = 10, max_concurrent: int = 4) -> List[Any]:
        """Process items in batches with async execution."""
        results = []
        
        # Split items into batches
        batches = [items[i:i + batch_size] for i in range(0, len(items), batch_size)]
        
        logger.info(f"Async processing {len(items)} items in {len(batches)} batches with {max_concurrent} concurrent tasks")
        
        # Create semaphore to limit concurrent tasks
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_batch_with_semaphore(batch):
            async with semaphore:
                return await self._async_process_batch(batch, async_process_func)
        
        # Process all batches concurrently
        batch_tasks = [process_batch_with_semaphore(batch) for batch in batches]
        batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
        
        # Flatten results
        for batch_result in batch_results:
            if isinstance(batch_result, Exception):
                logger.error(f"Async batch processing failed: {batch_result}")
                results.extend([None] * batch_size)  # Approximate, might be less
            else:
                results.extend(batch_result)
        
        return results
    
    async def _async_process_batch(self, batch: List[Any], async_process_func: Callable) -> List[Any]:
        """Process a single batch of items asynchronously."""
        tasks = []
        for item in batch:
            task = asyncio.create_task(self._safe_async_process(item, async_process_func))
            tasks.append(task)
        
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _safe_async_process(self, item: Any, async_process_func: Callable) -> Any:
        """Safely process a single item asynchronously."""
        try:
            return await async_process_func(item)
        except Exception as e:
            logger.error(f"Failed to async process item {item}: {e}")
            return None
    
    def rate_limit(self, calls_per_second: float = 1.0):
        """Decorator for rate limiting function calls."""
        min_interval = 1.0 / calls_per_second
        last_called = [0.0]
        
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                elapsed = time.time() - last_called[0]
                left_to_wait = min_interval - elapsed
                if left_to_wait > 0:
                    time.sleep(left_to_wait)
                
                last_called[0] = time.time()
                return func(*args, **kwargs)
            return wrapper
        return decorator
    
    def measure_performance(self, func: Callable) -> Callable:
        """Decorator to measure function performance."""
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.info(f"⏱️  {func.__name__} executed in {execution_time:.2f} seconds")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"⏱️  {func.__name__} failed after {execution_time:.2f} seconds: {e}")
                raise
        return wrapper
    
    def cleanup(self):
        """Cleanup resources."""
        self.clear_cache()
        self.thread_pool.shutdown(wait=True)
        logger.info("Performance optimizer cleaned up")


# Global instance
performance_optimizer = PerformanceOptimizer()


# Convenience decorators
def cached(ttl_seconds: int = 300):
    """Convenience decorator for caching."""
    return performance_optimizer.cached_result(ttl_seconds)


def rate_limited(calls_per_second: float = 1.0):
    """Convenience decorator for rate limiting."""
    return performance_optimizer.rate_limit(calls_per_second)


def timed(func: Callable) -> Callable:
    """Convenience decorator for timing."""
    return performance_optimizer.measure_performance(func)


class ConnectionPool:
    """Simple connection pool for database connections."""
    
    def __init__(self, create_connection_func: Callable, max_connections: int = 10):
        self.create_connection = create_connection_func
        self.max_connections = max_connections
        self.pool = []
        self.in_use = set()
        self.lock = threading.Lock()
    
    def get_connection(self):
        """Get a connection from the pool."""
        with self.lock:
            if self.pool:
                conn = self.pool.pop()
                self.in_use.add(conn)
                return conn
            elif len(self.in_use) < self.max_connections:
                conn = self.create_connection()
                self.in_use.add(conn)
                return conn
            else:
                raise Exception("Connection pool exhausted")
    
    def return_connection(self, conn):
        """Return a connection to the pool."""
        with self.lock:
            if conn in self.in_use:
                self.in_use.remove(conn)
                self.pool.append(conn)
    
    def close_all(self):
        """Close all connections."""
        with self.lock:
            for conn in self.pool + list(self.in_use):
                try:
                    conn.close()
                except:
                    pass
            self.pool.clear()
            self.in_use.clear()
